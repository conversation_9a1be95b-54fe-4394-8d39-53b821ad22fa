package com.yhl.scp.dfp.stock.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryShiftDao</code>
 * <p>
 * 库存推移表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 10:26:59
 */
public interface InventoryShiftDao extends BaseDao<InventoryShiftPO, InventoryShiftVO> {

    void deleteByVersionIdOemProduct(@Param("deliveryPlanVersionId") String deliveryPlanVersionId,
                                     @Param("oemCodes") List<String> oemCodes,
                                     @Param("productCodes") List<String> productCodes);

    void deleteByDeliveryPlanVersionIdAndProductCode(@Param("versionId") String versionId, @Param("productCode") String productCode);

    List<InventoryShiftVO> selectBohStockByParams(@Param("params") Map<String, Object> params);
    List<InventoryShiftVO> selectLatestDataByPlanDate(@Param("params") Map<String, Object> params);

    /**
     * 根据主机厂编码和物料编码查询最新的期初库存
     * @param params 查询参数，包含 oemCodes 和 productCodes
     * @return 库存推移数据列表
     */
    List<InventoryShiftVO> selectLatestOpeningInventoryByOemAndProduct(@Param("params") Map<String, Object> params);

    /**
     * 根据最新版本号查询receive汇总数据
     * @param params 查询参数，包含 oemCodes 和 productCodes
     * @return 库存推移数据列表
     */
    List<InventoryShiftVO> selectLatestReceiveSummaryByVersion(@Param("params") Map<String, Object> params);

}
