<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.stock.infrastructure.dao.InventoryShiftDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO">
        <!--@Table fdp_inventory_shift-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="routing_id" jdbcType="VARCHAR" property="routingId"/>
        <result column="total_supply_chain_days" jdbcType="INTEGER" property="totalSupplyChainDays"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="market_type" jdbcType="VARCHAR" property="marketType"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="part_name" jdbcType="VARCHAR" property="partName"/>
        <result column="planned_date" jdbcType="TIMESTAMP" property="plannedDate"/>
        <result column="delivery_date" jdbcType="TIMESTAMP" property="deliveryDate"/>
        <result column="opening_inventory" jdbcType="INTEGER" property="openingInventory"/>
        <result column="oem_opening_inventory" jdbcType="INTEGER" property="oemOpeningInventory"/>
        <result column="customer_demand" jdbcType="INTEGER" property="customerDemand"/>
        <result column="arrival_plan" jdbcType="INTEGER" property="arrivalPlan"/>
        <result column="delivery_plan" jdbcType="INTEGER" property="deliveryPlan"/>
        <result column="old_delivery_plan" jdbcType="INTEGER" property="oldDeliveryPlan"/>
        <result column="new_delivery_plan" jdbcType="INTEGER" property="newDeliveryPlan"/>
        <result column="receive" jdbcType="INTEGER" property="receive"/>
        <result column="old_receive" jdbcType="INTEGER" property="oldReceive"/>
        <result column="new_receive" jdbcType="INTEGER" property="newReceive"/>
        <result column="ending_inventory" jdbcType="INTEGER" property="endingInventory"/>
        <result column="ending_inventory_min_safe_diff" jdbcType="INTEGER" property="endingInventoryMinSafeDiff"/>
        <result column="oem_ending_inventory" jdbcType="INTEGER" property="oemEndingInventory"/>
        <result column="ending_inventory_days" jdbcType="VARCHAR" property="endingInventoryDays"/>
        <result column="old_ending_inventory_days" jdbcType="VARCHAR" property="oldEndingInventoryDays"/>
        <result column="new_ending_inventory_days" jdbcType="VARCHAR" property="newEndingInventoryDays"/>
        <result column="standard_safety_inventory_level" jdbcType="INTEGER" property="standardSafetyInventoryLevel"/>
        <result column="standard_safety_inventory_days" jdbcType="DECIMAL" property="standardSafetyInventoryDays"/>
        <result column="minimum_safety_inventory_days" jdbcType="DECIMAL" property="minimumSafetyInventoryDays"/>
        <result column="accumulated_inventory_gap" jdbcType="INTEGER" property="accumulatedInventoryGap"/>
        <result column="target_ending_inventory_days" jdbcType="VARCHAR" property="targetEndingInventoryDays"/>
        <result column="target_ending_inventory" jdbcType="INTEGER" property="targetEndingInventory"/>
        <result column="target_arrival_plan" jdbcType="INTEGER" property="targetArrivalPlan"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="old_opening_inventory" jdbcType="INTEGER" property="oldOpeningInventory"/>
        <result column="old_arrival_plan" jdbcType="INTEGER" property="oldArrivalPlan"/>
        <result column="old_ending_inventory" jdbcType="INTEGER" property="oldEndingInventory"/>
        <result column="new_opening_inventory" jdbcType="INTEGER" property="newOpeningInventory"/>
        <result column="new_arrival_plan" jdbcType="INTEGER" property="newArrivalPlan"/>
        <result column="new_ending_inventory" jdbcType="INTEGER" property="newEndingInventory"/>
        <result column="old_demand" jdbcType="INTEGER" property="oldDemand"/>
        <result column="new_demand" jdbcType="INTEGER" property="newDemand"/>
        <result column="old_oem_opening_inventory" jdbcType="INTEGER" property="oldOemOpeningInventory"/>
        <result column="new_oem_opening_inventory" jdbcType="INTEGER" property="newOemOpeningInventory"/>
        <result column="old_oem_ending_inventory" jdbcType="INTEGER" property="oldOemEndingInventory"/>
        <result column="new_oem_ending_inventory" jdbcType="INTEGER" property="newOemEndingInventory"/>
        <result column="switch_sign" jdbcType="VARCHAR" property="switchSign"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.stock.vo.InventoryShiftVO">
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,version_id,stock_point_code,routing_id,total_supply_chain_days,oem_code,oem_name,market_type,business_type,
        vehicle_model_code,product_code,part_name,planned_date,opening_inventory,oem_opening_inventory,customer_demand,
        arrival_plan,ending_inventory,ending_inventory_min_safe_diff,oem_ending_inventory,ending_inventory_days,old_ending_inventory_days,new_ending_inventory_days,
        standard_safety_inventory_level,standard_safety_inventory_days,minimum_safety_inventory_days,
        accumulated_inventory_gap,target_ending_inventory_days,target_ending_inventory,target_arrival_plan,remark,
        enabled,creator,create_time,modifier,modify_time,version_value,old_opening_inventory,old_arrival_plan,old_ending_inventory,
		new_opening_inventory,new_arrival_plan,new_ending_inventory,delivery_date,delivery_plan,receive,old_receive,new_receive,old_delivery_plan,new_delivery_plan,
        old_demand,new_demand,old_oem_opening_inventory,new_oem_opening_inventory,old_oem_ending_inventory,new_oem_ending_inventory,switch_sign
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,version_code
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.versionIds != null and params.versionIds.size() > 0">
                and version_id in
                <foreach collection="params.versionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.routingId != null and params.routingId != ''">
                and routing_id = #{params.routingId,jdbcType=VARCHAR}
            </if>
            <if test="params.totalSupplyChainDays != null">
                and total_supply_chain_days = #{params.totalSupplyChainDays,jdbcType=INTEGER}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodes != null and params.oemCodes.size > 0">
                and oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.oemName != null and params.oemName != ''">
                and oem_name = #{params.oemName,jdbcType=VARCHAR}
            </if>
            <if test="params.marketType != null and params.marketType != ''">
                and market_type = #{params.marketType,jdbcType=VARCHAR}
            </if>
            <if test="params.businessType != null and params.businessType != ''">
                and business_type = #{params.businessType,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.versionCode != null and params.versionCode != ''">
                and version_code = #{params.versionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.partName != null and params.partName != ''">
                and part_name = #{params.partName,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedDate != null">
                and planned_date = #{params.plannedDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.plannedDateList != null and params.plannedDateList.size() > 0">
                and planned_date in
                <foreach collection="params.plannedDateList" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=TIMESTAMP}
                </foreach>
            </if>
            <if test="params.plannedDateStrYMD != null and params.plannedDateStrYMD != ''">
                and date_format(planned_date,'%Y-%m-%d') <![CDATA[ >= ]]>  #{params.plannedDateStrYMD,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedDateEndYMD != null and params.plannedDateEndYMD != ''">
                AND date_format(planned_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{params.plannedDateEndYMD,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryDate != null">
                and delivery_date = #{params.deliveryDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.openingInventory != null">
                and opening_inventory = #{params.openingInventory,jdbcType=INTEGER}
            </if>
            <if test="params.customerDemand != null">
                and customer_demand = #{params.customerDemand,jdbcType=INTEGER}
            </if>
            <if test="params.arrivalPlan != null">
                and arrival_plan = #{params.arrivalPlan,jdbcType=INTEGER}
            </if>
            <if test="params.deliveryPlan != null">
                and delivery_plan = #{params.deliveryPlan,jdbcType=INTEGER}
            </if>
            <if test="params.oldDeliveryPlan != null">
                and old_delivery_plan = #{params.oldDeliveryPlan,jdbcType=INTEGER}
            </if>
            <if test="params.newDeliveryPlan != null">
                and new_delivery_plan = #{params.newDeliveryPlan,jdbcType=INTEGER}
            </if>
            <if test="params.receive != null">
                and receive = #{params.receive,jdbcType=INTEGER}
            </if>
            <if test="params.oldReceive != null">
                and old_receive = #{params.oldReceive,jdbcType=INTEGER}
            </if>
            <if test="params.newReceive != null">
                and new_receive = #{params.newReceive,jdbcType=INTEGER}
            </if>
            <if test="params.endingInventory != null">
                and ending_inventory = #{params.endingInventory,jdbcType=INTEGER}
            </if>
            <if test="params.endingInventoryDays != null">
                and ending_inventory_days = #{params.endingInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.oldEndingInventoryDays != null">
                and old_ending_inventory_days = #{params.oldEndingInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.newEndingInventoryDays != null">
                and new_ending_inventory_days = #{params.newEndingInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.standardSafetyInventoryLevel != null">
                and standard_safety_inventory_level = #{params.standardSafetyInventoryLevel,jdbcType=INTEGER}
            </if>
            <if test="params.standardSafetyInventoryDays != null">
                and standard_safety_inventory_days = #{params.standardSafetyInventoryDays,jdbcType=DECIMAL}
            </if>
            <if test="params.minimumSafetyInventoryDays != null">
                and minimum_safety_inventory_days = #{params.minimumSafetyInventoryDays,jdbcType=DECIMAL}
            </if>
            <if test="params.accumulatedInventoryGap != null">
                and accumulated_inventory_gap = #{params.accumulatedInventoryGap,jdbcType=INTEGER}
            </if>
            <if test="params.targetEndingInventoryDays != null">
                and target_ending_inventory_days = #{params.targetEndingInventoryDays,jdbcType=VARCHAR}
            </if>
            <if test="params.targetEndingInventory != null">
                and target_ending_inventory = #{params.targetEndingInventory,jdbcType=INTEGER}
            </if>
            <if test="params.targetArrivalPlan != null">
                and target_arrival_plan = #{params.targetArrivalPlan,jdbcType=INTEGER}
            </if>
            <if test="params.oemOpeningInventory != null">
                and oem_opening_inventory = #{params.oemOpeningInventory,jdbcType=INTEGER}
            </if>
            <if test="params.oemEndingInventory != null">
                and oem_ending_inventory = #{params.oemEndingInventory,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.oldOpeningInventory != null">
                and old_opening_inventory = #{params.oldOpeningInventory,jdbcType=INTEGER}
            </if>
			<if test="params.oldArrivalPlan != null">
                and old_arrival_plan = #{params.oldArrivalPlan,jdbcType=INTEGER}
            </if>
			<if test="params.oldEndingInventory != null">
                and old_ending_inventory = #{params.oldEndingInventory,jdbcType=INTEGER}
            </if>
            <if test="params.newOpeningInventory != null">
                and new_opening_inventory = #{params.newOpeningInventory,jdbcType=INTEGER}
            </if>
			<if test="params.newArrivalPlan != null">
                and new_arrival_plan = #{params.newArrivalPlan,jdbcType=INTEGER}
            </if>
			<if test="params.newEndingInventory != null">
                and new_ending_inventory = #{params.newEndingInventory,jdbcType=INTEGER}
            </if>
            <if test="params.oldDemand != null">
                and old_demand = #{params.oldDemand,jdbcType=INTEGER}
            </if>
            <if test="params.newDemand != null">
                and new_demand = #{params.newDemand,jdbcType=INTEGER}
            </if>
            <if test="params.oldOemOpeningInventory != null">
                and old_oem_opening_inventory = #{params.oldOemOpeningInventory,jdbcType=INTEGER}
            </if>
            <if test="params.newOemOpeningInventory != null">
                and new_oem_opening_inventory = #{params.newOemOpeningInventory,jdbcType=INTEGER}
            </if>
            <if test="params.oldOemEndingInventory != null">
                and old_oem_ending_inventory = #{params.oldOemEndingInventory,jdbcType=INTEGER}
            </if>
            <if test="params.newOemEndingInventory != null">
                and new_oem_ending_inventory = #{params.newOemEndingInventory,jdbcType=INTEGER}
            </if>
            <if test="params.switchSign != null">
                and switch_sign = #{params.switchSign,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_inventory_shift
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_inventory_shift
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_inventory_shift
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_inventory_shift
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_inventory_shift
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectBohStockByParams" resultMap="VOResultMap">
        select shift.oem_code,
               shift.product_code,
               IF(shift.opening_inventory IS NULL, 0, shift.opening_inventory) as opening_inventory,
               IF(shift.oem_opening_inventory IS NULL, 0, shift.oem_opening_inventory) as oem_opening_inventory,
               shift.planned_date
        from fdp_inventory_shift shift
        where shift.version_id = (select id from fdp_delivery_plan_version order by version_code desc limit 1)
          and shift.planned_date = CURDATE()
        <if test="params.productCodes != null and params.productCodes.size > 0">
            and shift.product_code in
            <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_inventory_shift(
        id,
        version_id,
        stock_point_code,
        routing_id,
        total_supply_chain_days,
        oem_code,
        oem_name,
        market_type,
        business_type,
        vehicle_model_code,
        product_code,
        part_name,
        planned_date,
        delivery_date,
        opening_inventory,
        oem_opening_inventory,
        customer_demand,
        arrival_plan,
        delivery_plan,
        old_delivery_plan,
        new_delivery_plan,
        receive,
        old_receive,
        new_receive,
        ending_inventory,
        oem_ending_inventory,
        ending_inventory_min_safe_diff,
        ending_inventory_days,
        old_ending_inventory_days,
        new_ending_inventory_days,
        standard_safety_inventory_level,
        standard_safety_inventory_days,
        minimum_safety_inventory_days,
        accumulated_inventory_gap,
        target_ending_inventory_days,
        target_ending_inventory,
        target_arrival_plan,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        old_opening_inventory,
		old_arrival_plan,
		old_ending_inventory,
		new_opening_inventory,
		new_arrival_plan,
		new_ending_inventory,
        old_demand,
        new_demand,
        old_oem_opening_inventory,
        new_oem_epening_inventory,
        old_oem_ending_inventory,
        new_oem_ending_inventory,
        switch_sign)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{totalSupplyChainDays,jdbcType=INTEGER},
        #{oemCode,jdbcType=VARCHAR},
        #{oemName,jdbcType=VARCHAR},
        #{marketType,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{partName,jdbcType=VARCHAR},
        #{plannedDate,jdbcType=TIMESTAMP},
        #{deliveryDate,jdbcType=TIMESTAMP},
        #{openingInventory,jdbcType=INTEGER},
        #{oemOpeningInventory,jdbcType=INTEGER},
        #{customerDemand,jdbcType=INTEGER},
        #{arrivalPlan,jdbcType=INTEGER},
        #{deliveryPlan,jdbcType=INTEGER},
        #{oldDeliveryPlan,jdbcType=INTEGER},
        #{newDeliveryPlan,jdbcType=INTEGER},
        #{receive,jdbcType=INTEGER},
        #{oldReceive,jdbcType=INTEGER},
        #{newReceive,jdbcType=INTEGER},
        #{endingInventory,jdbcType=INTEGER},
        #{oemEndingInventory,jdbcType=INTEGER},
        #{endingInventoryMinSafeDiff,jdbcType=INTEGER},
        #{endingInventoryDays,jdbcType=VARCHAR},
        #{oldEndingInventoryDays,jdbcType=VARCHAR},
        #{newEndingInventoryDays,jdbcType=VARCHAR},
        #{standardSafetyInventoryLevel,jdbcType=INTEGER},
        #{standardSafetyInventoryDays,jdbcType=DECIMAL},
        #{minimumSafetyInventoryDays,jdbcType=DECIMAL},
        #{accumulatedInventoryGap,jdbcType=INTEGER},
        #{targetEndingInventoryDays,jdbcType=VARCHAR},
        #{targetEndingInventory,jdbcType=INTEGER},
        #{targetArrivalPlan,jdbcType=INTEGER},
        #{oemOpeningInventory,jdbcType=INTEGER},
        #{oemEndingInventory,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{oldOpeningInventory,jdbcType=INTEGER},
        #{oldArrivalPlan,jdbcType=INTEGER},
        #{oldEndingInventory,jdbcType=INTEGER},
        #{newOpeningInventory,jdbcType=INTEGER},
        #{newArrivalPlan,jdbcType=INTEGER},
        #{newEndingInventory,jdbcType=INTEGER},
            #{oldDemand,jdbcType=INTEGER},
            #{newDemand,jdbcType=INTEGER},
            #{oldOemOpeningInventory,jdbcType=INTEGER},
            #{newOemOpeningInventory,jdbcType=INTEGER},
            #{oldOemEndingInventory,jdbcType=INTEGER},
            #{newOemEndingInventory,jdbcType=INTEGER},
            #{switchSign,jdbcType=VARCHAR}
        )
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO">
        insert into fdp_inventory_shift(
        id,
        version_id,
        stock_point_code,
        routing_id,
        total_supply_chain_days,
        oem_code,
        oem_name,
        market_type,
        business_type,
        vehicle_model_code,
        product_code,
        part_name,
        planned_date,
        delivery_date,
        opening_inventory,
        oem_opening_inventory,
        customer_demand,
        arrival_plan,
        delivery_plan,
        old_delivery_plan,
        new_delivery_plan,
        receive,
        old_receive,
        new_receive,
        ending_inventory,
        oem_ending_inventory,
        ending_inventory_min_safe_diff,
        ending_inventory_days,
        old_ending_inventory_days,
        new_ending_inventory_days,
        standard_safety_inventory_level,
        standard_safety_inventory_days,
        minimum_safety_inventory_days,
        accumulated_inventory_gap,
        target_ending_inventory_days,
        target_ending_inventory,
        target_arrival_plan,
        oem_opening_inventory,
        oem_ending_inventory,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        old_opening_inventory,
		old_arrival_plan,
		old_ending_inventory,
		new_opening_inventory,
		new_arrival_plan,
		new_ending_inventory,
        old_demand,
        new_demand,
        old_oem_opening_inventory,
        new_oem_opening_inventory,
        old_oem_ending_inventory,
        new_oem_ending_inventory,
        switch_sign)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{routingId,jdbcType=VARCHAR},
        #{totalSupplyChainDays,jdbcType=INTEGER},
        #{oemCode,jdbcType=VARCHAR},
        #{oemName,jdbcType=VARCHAR},
        #{marketType,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{partName,jdbcType=VARCHAR},
        #{plannedDate,jdbcType=TIMESTAMP},
        #{deliveryDate,jdbcType=TIMESTAMP},
        #{openingInventory,jdbcType=INTEGER},
        #{oemOpeningInventory,jdbcType=INTEGER},
        #{customerDemand,jdbcType=INTEGER},
        #{arrivalPlan,jdbcType=INTEGER},
        #{deliveryPlan,jdbcType=INTEGER},
        #{oldDeliveryPlan,jdbcType=INTEGER},
        #{newDeliveryPlan,jdbcType=INTEGER},
        #{receive,jdbcType=INTEGER},
        #{oldReceive,jdbcType=INTEGER},
        #{newReceive,jdbcType=INTEGER},
        #{endingInventory,jdbcType=INTEGER},
        #{oemEndingInventory,jdbcType=INTEGER},
        #{endingInventoryMinSafeDiff,jdbcType=INTEGER},
        #{endingInventoryDays,jdbcType=VARCHAR},
        #{oldEndingInventoryDays,jdbcType=VARCHAR},
        #{newEndingInventoryDays,jdbcType=VARCHAR},
        #{standardSafetyInventoryLevel,jdbcType=INTEGER},
        #{standardSafetyInventoryDays,jdbcType=DECIMAL},
        #{minimumSafetyInventoryDays,jdbcType=DECIMAL},
        #{accumulatedInventoryGap,jdbcType=INTEGER},
        #{targetEndingInventoryDays,jdbcType=VARCHAR},
        #{targetEndingInventory,jdbcType=INTEGER},
        #{targetArrivalPlan,jdbcType=INTEGER},
        #{oemOpeningInventory,jdbcType=INTEGER},
        #{oemEndingInventory,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{oldOpeningInventory,jdbcType=INTEGER},
        #{oldArrivalPlan,jdbcType=INTEGER},
        #{oldEndingInventory,jdbcType=INTEGER},
        #{newOpeningInventory,jdbcType=INTEGER},
        #{newArrivalPlan,jdbcType=INTEGER},
        #{newEndingInventory,jdbcType=INTEGER},
        #{oldDemand,jdbcType=INTEGER},
        #{newDemand,jdbcType=INTEGER},
        #{oldOemOpeningInventory,jdbcType=INTEGER},
        #{newOemOpeningInventory,jdbcType=INTEGER},
        #{oldOemEndingInventory,jdbcType=INTEGER},
        #{newOemEndingInventory,jdbcType=INTEGER},
        #{switchSign,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_inventory_shift(
        id,
        version_id,
        stock_point_code,
        routing_id,
        total_supply_chain_days,
        oem_code,
        oem_name,
        market_type,
        business_type,
        vehicle_model_code,
        product_code,
        part_name,
        planned_date,
        delivery_date,
        opening_inventory,
        oem_opening_inventory,
        customer_demand,
        arrival_plan,
        delivery_plan,
        old_delivery_plan,
        new_delivery_plan,
        receive,
        old_receive,
        new_receive,
        ending_inventory,
        oem_ending_inventory,
        ending_inventory_min_safe_diff,
        ending_inventory_days,
        old_ending_inventory_days,
        new_ending_inventory_days,
        standard_safety_inventory_level,
        standard_safety_inventory_days,
        minimum_safety_inventory_days,
        accumulated_inventory_gap,
        target_ending_inventory_days,
        target_ending_inventory,
        target_arrival_plan,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        old_opening_inventory,
		old_arrival_plan,
		old_ending_inventory,
		new_opening_inventory,
		new_arrival_plan,
		new_ending_inventory,
        old_demand,
        new_demand,
        old_oem_opening_inventory,
        new_oem_opening_inventory,
        old_oem_ending_inventory,
        new_oem_ending_inventory,
        switch_sign)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.routingId,jdbcType=VARCHAR},
        #{entity.totalSupplyChainDays,jdbcType=INTEGER},
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.oemName,jdbcType=VARCHAR},
        #{entity.marketType,jdbcType=VARCHAR},
        #{entity.businessType,jdbcType=VARCHAR},
        #{entity.vehicleModelCode,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.partName,jdbcType=VARCHAR},
        #{entity.plannedDate,jdbcType=TIMESTAMP},
        #{entity.deliveryDate,jdbcType=TIMESTAMP},
        #{entity.openingInventory,jdbcType=INTEGER},
        #{entity.oemOpeningInventory,jdbcType=INTEGER},
        #{entity.customerDemand,jdbcType=INTEGER},
        #{entity.arrivalPlan,jdbcType=INTEGER},
        #{entity.deliveryPlan,jdbcType=INTEGER},
        #{entity.oldDeliveryPlan,jdbcType=INTEGER},
        #{entity.newDeliveryPlan,jdbcType=INTEGER},
        #{entity.receive,jdbcType=INTEGER},
        #{entity.oldReceive,jdbcType=INTEGER},
        #{entity.newReceive,jdbcType=INTEGER},
        #{entity.endingInventory,jdbcType=INTEGER},
        #{entity.oemEndingInventory,jdbcType=INTEGER},
        #{entity.endingInventoryMinSafeDiff,jdbcType=INTEGER},
        #{entity.endingInventoryDays,jdbcType=VARCHAR},
        #{entity.oldEndingInventoryDays,jdbcType=VARCHAR},
        #{entity.newEndingInventoryDays,jdbcType=VARCHAR},
        #{entity.standardSafetyInventoryLevel,jdbcType=INTEGER},
        #{entity.standardSafetyInventoryDays,jdbcType=DECIMAL},
        #{entity.minimumSafetyInventoryDays,jdbcType=DECIMAL},
        #{entity.accumulatedInventoryGap,jdbcType=INTEGER},
        #{entity.targetEndingInventoryDays,jdbcType=VARCHAR},
        #{entity.targetEndingInventory,jdbcType=INTEGER},
        #{entity.targetArrivalPlan,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.oldOpeningInventory,jdbcType=INTEGER},
        #{entity.oldArrivalPlan,jdbcType=INTEGER},
        #{entity.oldEndingInventory,jdbcType=INTEGER},
        #{entity.newOpeningInventory,jdbcType=INTEGER},
        #{entity.newArrivalPlan,jdbcType=INTEGER},
        #{entity.newEndingInventory,jdbcType=INTEGER},
         #{entity.oldDemand,jdbcType=INTEGER},
         #{entity.newDemand,jdbcType=INTEGER},
         #{entity.oldOemOpeningInventory,jdbcType=INTEGER},
         #{entity.newOemOpeningInventory,jdbcType=INTEGER},
         #{entity.oldOemEndingInventory,jdbcType=INTEGER},
         #{entity.newOemEndingInventory,jdbcType=INTEGER},
         #{entity.switchSign,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_inventory_shift(
        id,
        version_id,
        stock_point_code,
        routing_id,
        total_supply_chain_days,
        oem_code,
        oem_name,
        market_type,
        business_type,
        vehicle_model_code,
        product_code,
        part_name,
        planned_date,
        delivery_date,
        opening_inventory,
        oem_opening_inventory,
        customer_demand,
        arrival_plan,
        delivery_plan,
        old_delivery_plan,
        new_delivery_plan,
        receive,
        old_receive,
        new_receive,
        ending_inventory,
        oem_ending_inventory,
        ending_inventory_min_safe_diff,
        ending_inventory_days,
        old_ending_inventory_days,
        new_ending_inventory_days,
        standard_safety_inventory_level,
        standard_safety_inventory_days,
        minimum_safety_inventory_days,
        accumulated_inventory_gap,
        target_ending_inventory_days,
        target_ending_inventory,
        target_arrival_plan,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        old_opening_inventory,
		old_arrival_plan,
		old_ending_inventory,
		new_opening_inventory,
		new_arrival_plan,
		new_ending_inventory,
        old_demand,
        new_demand,
        old_oem_opening_inventory,
        new_oem_opening_inventory,
        old_oem_ending_inventory,
        new_oem_ending_inventory,
        switch_sign)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.routingId,jdbcType=VARCHAR},
        #{entity.totalSupplyChainDays,jdbcType=INTEGER},
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.oemName,jdbcType=VARCHAR},
        #{entity.marketType,jdbcType=VARCHAR},
        #{entity.businessType,jdbcType=VARCHAR},
        #{entity.vehicleModelCode,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.partName,jdbcType=VARCHAR},
        #{entity.plannedDate,jdbcType=TIMESTAMP},
        #{entity.deliveryDate,jdbcType=TIMESTAMP},
        #{entity.openingInventory,jdbcType=INTEGER},
        #{entity.oemOpeningInventory,jdbcType=INTEGER},
        #{entity.customerDemand,jdbcType=INTEGER},
        #{entity.arrivalPlan,jdbcType=INTEGER},
        #{entity.deliveryPlan,jdbcType=INTEGER},
        #{entity.oldDeliveryPlan,jdbcType=INTEGER},
        #{entity.newDeliveryPlan,jdbcType=INTEGER},
        #{entity.receive,jdbcType=INTEGER},
        #{entity.oldReceive,jdbcType=INTEGER},
        #{entity.newReceive,jdbcType=INTEGER},
        #{entity.endingInventory,jdbcType=INTEGER},
        #{entity.oemEndingInventory,jdbcType=INTEGER},
        #{entity.endingInventoryMinSafeDiff,jdbcType=INTEGER},
        #{entity.endingInventoryDays,jdbcType=VARCHAR},
        #{entity.oldEndingInventoryDays,jdbcType=VARCHAR},
        #{entity.newEndingInventoryDays,jdbcType=VARCHAR},
        #{entity.standardSafetyInventoryLevel,jdbcType=INTEGER},
        #{entity.standardSafetyInventoryDays,jdbcType=DECIMAL},
        #{entity.minimumSafetyInventoryDays,jdbcType=DECIMAL},
        #{entity.accumulatedInventoryGap,jdbcType=INTEGER},
        #{entity.targetEndingInventoryDays,jdbcType=VARCHAR},
        #{entity.targetEndingInventory,jdbcType=INTEGER},
        #{entity.targetArrivalPlan,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.oldOpeningInventory,jdbcType=INTEGER},
        #{entity.oldArrivalPlan,jdbcType=INTEGER},
        #{entity.oldEndingInventory,jdbcType=INTEGER},
        #{entity.newOpeningInventory,jdbcType=INTEGER},
        #{entity.newArrivalPlan,jdbcType=INTEGER},
        #{entity.newEndingInventory,jdbcType=INTEGER},
         #{entity.oldDemand,jdbcType=INTEGER},
         #{entity.newDemand,jdbcType=INTEGER},
         #{entity.oldOemOpeningInventory,jdbcType=INTEGER},
         #{entity.newOemOpeningInventory,jdbcType=INTEGER},
         #{entity.oldOemEndingInventory,jdbcType=INTEGER},
         #{entity.newOemEndingInventory,jdbcType=INTEGER},
         #{entity.switchSign,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO">
        update fdp_inventory_shift set
        version_id = #{versionId,jdbcType=VARCHAR},
        stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
        routing_id = #{routingId,jdbcType=VARCHAR},
        total_supply_chain_days = #{totalSupplyChainDays,jdbcType=INTEGER},
        oem_code = #{oemCode,jdbcType=VARCHAR},
        oem_name = #{oemName,jdbcType=VARCHAR},
        market_type = #{marketType,jdbcType=VARCHAR},
        business_type = #{businessType,jdbcType=VARCHAR},
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        part_name = #{partName,jdbcType=VARCHAR},
        planned_date = #{plannedDate,jdbcType=TIMESTAMP},
        delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
        opening_inventory = #{openingInventory,jdbcType=INTEGER},
        oem_opening_inventory = #{oemOpeningInventory,jdbcType=INTEGER},
        customer_demand = #{customerDemand,jdbcType=INTEGER},
        arrival_plan = #{arrivalPlan,jdbcType=INTEGER},
        delivery_plan = #{deliveryPlan,jdbcType=INTEGER},
        old_delivery_plan = #{oldDeliveryPlan,jdbcType=INTEGER},
        new_delivery_plan = #{newDeliveryPlan,jdbcType=INTEGER},
        receive = #{receive,jdbcType=INTEGER},
        old_receive = #{oldReceive,jdbcType=INTEGER},
        new_receive = #{newReceive,jdbcType=INTEGER},
        ending_inventory = #{endingInventory,jdbcType=INTEGER},
        oem_ending_inventory = #{oemEndingInventory,jdbcType=INTEGER},
        ending_inventory_min_safe_diff = #{endingInventoryMinSafeDiff,jdbcType=INTEGER},
        ending_inventory_days = #{endingInventoryDays,jdbcType=VARCHAR},
        old_ending_inventory_days = #{oldEndingInventoryDays,jdbcType=VARCHAR},
        new_ending_inventory_days = #{newEndingInventoryDays,jdbcType=VARCHAR},
        standard_safety_inventory_level = #{standardSafetyInventoryLevel,jdbcType=INTEGER},
        standard_safety_inventory_days = #{standardSafetyInventoryDays,jdbcType=DECIMAL},
        minimum_safety_inventory_days = #{minimumSafetyInventoryDays,jdbcType=DECIMAL},
        accumulated_inventory_gap = #{accumulatedInventoryGap,jdbcType=INTEGER},
        target_ending_inventory_days = #{targetEndingInventoryDays,jdbcType=VARCHAR},
        target_ending_inventory = #{targetEndingInventory,jdbcType=INTEGER},
        target_arrival_plan = #{targetArrivalPlan,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        old_opening_inventory = #{oldOpeningInventory,jdbcType=INTEGER},
        old_arrival_plan = #{oldArrivalPlan,jdbcType=INTEGER},
        old_ending_inventory = #{oldEndingInventory,jdbcType=INTEGER},
        new_opening_inventory = #{newOpeningInventory,jdbcType=INTEGER},
        new_arrival_plan = #{newArrivalPlan,jdbcType=INTEGER},
        new_ending_inventory = #{newEndingInventory,jdbcType=INTEGER},
        old_demand = #{oldDemand,jdbcType=INTEGER},
        new_demand = #{newDemand,jdbcType=INTEGER},
        old_oem_opening_inventory = #{oldOemOpeningInventory,jdbcType=INTEGER},
        new_oem_opening_inventory = #{newOemOpeningInventory,jdbcType=INTEGER},
        old_oem_ending_inventory = #{oldOemEndingInventory,jdbcType=INTEGER},
        new_oem_ending_inventory = #{newOemEndingInventory,jdbcType=INTEGER},
        switch_sign = #{switchSign,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO">
        update fdp_inventory_shift
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.totalSupplyChainDays != null">
                total_supply_chain_days = #{item.totalSupplyChainDays,jdbcType=INTEGER},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemName != null and item.oemName != ''">
                oem_name = #{item.oemName,jdbcType=VARCHAR},
            </if>
            <if test="item.marketType != null and item.marketType != ''">
                market_type = #{item.marketType,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partName != null and item.partName != ''">
                part_name = #{item.partName,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedDate != null">
                planned_date = #{item.plannedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.deliveryDate != null">
                delivery_date = #{item.deliveryDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.openingInventory != null">
                opening_inventory = #{item.openingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oemOpeningInventory != null">
                oem_opening_inventory = #{item.oemOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.customerDemand != null">
                customer_demand = #{item.customerDemand,jdbcType=INTEGER},
            </if>
            <if test="item.arrivalPlan != null">
                arrival_plan = #{item.arrivalPlan,jdbcType=INTEGER},
            </if>
            <if test="item.deliveryPlan != null">
                delivery_plan = #{item.deliveryPlan,jdbcType=INTEGER},
            </if>
            <if test="item.oldDeliveryPlan != null">
                old_delivery_plan = #{item.oldDeliveryPlan,jdbcType=INTEGER},
            </if>
            <if test="item.newDeliveryPlan != null">
                new_delivery_plan = #{item.newDeliveryPlan,jdbcType=INTEGER},
            </if>
            <if test="item.receive != null">
                receive = #{item.receive,jdbcType=INTEGER},
            </if>
            <if test="item.oldReceive != null">
                old_receive = #{item.oldReceive,jdbcType=INTEGER},
            </if>
            <if test="item.newReceive != null">
                new_receive = #{item.newReceive,jdbcType=INTEGER},
            </if>
            <if test="item.endingInventory != null">
                ending_inventory = #{item.endingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oemEndingInventory != null">
                oem_ending_inventory = #{item.oemEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.endingInventoryMinSafeDiff != null">
                ending_inventory_min_safe_diff = #{item.endingInventoryMinSafeDiff,jdbcType=INTEGER},
            </if>
            <if test="item.endingInventoryDays != null">
                ending_inventory_days = #{item.endingInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.oldEndingInventoryDays != null">
                old_ending_inventory_days = #{item.oldEndingInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.newEndingInventoryDays != null">
                new_ending_inventory_days = #{item.newEndingInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.standardSafetyInventoryLevel != null">
                standard_safety_inventory_level = #{item.standardSafetyInventoryLevel,jdbcType=INTEGER},
            </if>
            <if test="item.standardSafetyInventoryDays != null">
                standard_safety_inventory_days = #{item.standardSafetyInventoryDays,jdbcType=DECIMAL},
            </if>
            <if test="item.minimumSafetyInventoryDays != null">
                minimum_safety_inventory_days = #{item.minimumSafetyInventoryDays,jdbcType=DECIMAL},
            </if>
            <if test="item.accumulatedInventoryGap != null">
                accumulated_inventory_gap = #{item.accumulatedInventoryGap,jdbcType=INTEGER},
            </if>
            <if test="item.targetEndingInventoryDays != null">
                target_ending_inventory_days = #{item.targetEndingInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.targetEndingInventory != null">
                target_ending_inventory = #{item.targetEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.targetArrivalPlan != null">
                target_arrival_plan = #{item.targetArrivalPlan,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.oldOpeningInventory != null">
                old_opening_inventory = #{item.oldOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oldArrivalPlan != null">
                old_arrival_plan = #{item.oldArrivalPlan,jdbcType=INTEGER},
            </if>
            <if test="item.oldEndingInventory != null">
                old_ending_inventory = #{item.oldEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.newOpeningInventory != null">
                new_opening_inventory = #{item.newOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.newArrivalPlan != null">
                new_arrival_plan = #{item.newArrivalPlan,jdbcType=INTEGER},
            </if>
            <if test="item.newEndingInventory != null">
                new_ending_inventory = #{item.newEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oldDemand != null">
                old_demand = #{item.oldDemand,jdbcType=INTEGER},
            </if>
            <if test="item.newDemand != null">
                new_demand = #{item.newDemand,jdbcType=INTEGER},
            </if>
            <if test="item.oldOemOpeningInventory != null">
                old_oem_opening_inventory = #{item.oldOemOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.newOemOpeningInventory != null">
                new_oem_opening_inventory = #{item.newOemOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oldOemEndingInventory != null">
                old_oem_ending_inventory = #{item.oldOemEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.newOemEndingInventory != null">
                new_oem_ending_inventory = #{item.newOemEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.switchSign != null">
                switch_sign = #{item.switchSign,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_inventory_shift
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="total_supply_chain_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.totalSupplyChainDays,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="market_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.marketType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="business_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.businessType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planned_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plannedDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="delivery_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.openingInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="oem_opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemOpeningInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="customer_demand = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerDemand,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="arrival_plan = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.arrivalPlan,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="delivery_plan = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryPlan,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_delivery_plan = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldDeliveryPlan,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="new_delivery_plan = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newDeliveryPlan,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="receive = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receive,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_receive = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldReceive,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="new_receive = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newReceive,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endingInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="oem_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemEndingInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ending_inventory_min_safe_diff = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endingInventoryMinSafeDiff,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="ending_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endingInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="old_ending_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldEndingInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="new_ending_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newEndingInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_safety_inventory_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardSafetyInventoryLevel,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="standard_safety_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardSafetyInventoryDays,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="minimum_safety_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minimumSafetyInventoryDays,jdbcType=DECIMAL}
                </foreach>
            </trim>
            <trim prefix="accumulated_inventory_gap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.accumulatedInventoryGap,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="target_ending_inventory_days = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.targetEndingInventoryDays,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="target_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.targetEndingInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="target_arrival_plan = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.targetArrivalPlan,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldOpeningInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_arrival_plan = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldArrivalPlan,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldEndingInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="new_opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newOpeningInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="new_arrival_plan = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newArrivalPlan,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="new_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newEndingInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_demand = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldDemand,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="new_demand = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newDemand,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_oem_opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldOemOpeningInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="new_oem_opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newOemOpeningInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_oem_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldOemEndingInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="new_oem_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newOemEndingInventory,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="switch_sign = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.switchSign,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_inventory_shift 
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.routingId != null and item.routingId != ''">
                routing_id = #{item.routingId,jdbcType=VARCHAR},
            </if>
            <if test="item.totalSupplyChainDays != null">
                total_supply_chain_days = #{item.totalSupplyChainDays,jdbcType=INTEGER},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oemName != null and item.oemName != ''">
                oem_name = #{item.oemName,jdbcType=VARCHAR},
            </if>
            <if test="item.marketType != null and item.marketType != ''">
                market_type = #{item.marketType,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.partName != null and item.partName != ''">
                part_name = #{item.partName,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedDate != null">
                planned_date = #{item.plannedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.deliveryDate != null">
                delivery_date = #{item.deliveryDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.openingInventory != null">
                opening_inventory = #{item.openingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oemOpeningInventory != null">
                oem_opening_inventory = #{item.oemOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.customerDemand != null">
                customer_demand = #{item.customerDemand,jdbcType=INTEGER},
            </if>
            <if test="item.arrivalPlan != null">
                arrival_plan = #{item.arrivalPlan,jdbcType=INTEGER},
            </if>
            <if test="item.deliveryPlan != null">
                delivery_plan = #{item.deliveryPlan,jdbcType=INTEGER},
            </if>
            <if test="item.oldDeliveryPlan != null">
                old_delivery_plan = #{item.oldDeliveryPlan,jdbcType=INTEGER},
            </if>
            <if test="item.newDeliveryPlan != null">
                new_delivery_plan = #{item.newDeliveryPlan,jdbcType=INTEGER},
            </if>
            <if test="item.receive != null">
                receive = #{item.receive,jdbcType=INTEGER},
            </if>
            <if test="item.oldReceive != null">
                old_receive = #{item.oldReceive,jdbcType=INTEGER},
            </if>
            <if test="item.newReceive != null">
                new_receive = #{item.newReceive,jdbcType=INTEGER},
            </if>
            <if test="item.endingInventory != null">
                ending_inventory = #{item.endingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.endingInventoryMinSafeDiff != null">
                ending_inventory_min_safe_diff = #{item.endingInventoryMinSafeDiff,jdbcType=INTEGER},
            </if>
            <if test="item.oemEndingInventory != null">
                oem_ending_inventory = #{item.oemEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.endingInventoryDays != null">
                ending_inventory_days = #{item.endingInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.oldEndingInventoryDays != null">
                old_ending_inventory_days = #{item.oldEndingInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.newEndingInventoryDays != null">
                new_ending_inventory_days = #{item.newEndingInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.standardSafetyInventoryLevel != null">
                standard_safety_inventory_level = #{item.standardSafetyInventoryLevel,jdbcType=INTEGER},
            </if>
            <if test="item.standardSafetyInventoryDays != null">
                standard_safety_inventory_days = #{item.standardSafetyInventoryDays,jdbcType=DECIMAL},
            </if>
            <if test="item.minimumSafetyInventoryDays != null">
                minimum_safety_inventory_days = #{item.minimumSafetyInventoryDays,jdbcType=DECIMAL},
            </if>
            <if test="item.accumulatedInventoryGap != null">
                accumulated_inventory_gap = #{item.accumulatedInventoryGap,jdbcType=INTEGER},
            </if>
            <if test="item.targetEndingInventoryDays != null">
                target_ending_inventory_days = #{item.targetEndingInventoryDays,jdbcType=VARCHAR},
            </if>
            <if test="item.targetEndingInventory != null">
                target_ending_inventory = #{item.targetEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.targetArrivalPlan != null">
                target_arrival_plan = #{item.targetArrivalPlan,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.oldOpeningInventory != null">
                old_opening_inventory = #{item.oldOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oldArrivalPlan != null">
                old_arrival_plan = #{item.oldArrivalPlan,jdbcType=INTEGER},
            </if>
            <if test="item.oldEndingInventory != null">
                old_ending_inventory = #{item.oldEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.newOpeningInventory != null">
                new_opening_inventory = #{item.newOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.newArrivalPlan != null">
                new_arrival_plan = #{item.newArrivalPlan,jdbcType=INTEGER},
            </if>
            <if test="item.newEndingInventory != null">
                new_ending_inventory = #{item.newEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oldDemand != null">
                old_demand = #{item.oldDemand,jdbcType=INTEGER},
            </if>
            <if test="item.newDemand != null">
                new_demand = #{item.newDemand,jdbcType=INTEGER},
            </if>
            <if test="item.oldOemOpeningInventory != null">
                old_oem_opening_inventory = #{item.oldOemOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.newOemOpeningInventory != null">
                new_oem_opening_inventory = #{item.newOemOpeningInventory,jdbcType=INTEGER},
            </if>
            <if test="item.oldOemEndingInventory != null">
                old_oem_ending_inventory = #{item.oldOemEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.newOemEndingInventory != null">
                new_oem_ending_inventory = #{item.newOemEndingInventory,jdbcType=INTEGER},
            </if>
            <if test="item.switchSign != null">
                switch_sign = #{item.switchSign,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_inventory_shift
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_inventory_shift where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteByVersionIdOemProduct" parameterType="java.util.List">
        delete from fdp_inventory_shift
        where
        version_id = #{deliveryPlanVersionId,jdbcType=VARCHAR}
        <if test="oemCodes != null and oemCodes.size() > 0">
            and oem_code in
            <foreach collection="oemCodes" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="productCodes != null and productCodes.size() > 0">
            and product_code in
            <foreach collection="productCodes" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </delete>

    <delete id="deleteByDeliveryPlanVersionIdAndProductCode">
        delete from fdp_inventory_shift where version_id = #{versionId,jdbcType=VARCHAR} and product_code = #{productCode,jdbcType=VARCHAR}
    </delete>
    <select id="selectLatestDataByPlanDate" resultMap="VOResultMap">
        SELECT t.*
        FROM fdp_inventory_shift t
        JOIN (
        SELECT planned_date, MAX(modify_time) AS max_modify_time
        FROM fdp_inventory_shift
        GROUP BY planned_date
        ) latest
        ON t.planned_date = latest.planned_date
        AND t.modify_time = latest.max_modify_time
        where 1=1
        <if test="params.plannedDateStrYMD != null and params.plannedDateStrYMD != ''">
            and date_format(t.planned_date,'%Y-%m-%d') <![CDATA[ >= ]]>  #{params.plannedDateStrYMD,jdbcType=VARCHAR}
        </if>
        <if test="params.plannedDateEndYMD != null and params.plannedDateEndYMD != ''">
            AND date_format(t.planned_date,'%Y-%m-%d') <![CDATA[ <= ]]> #{params.plannedDateEndYMD,jdbcType=VARCHAR}
        </if>
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            and t.product_code in
            <foreach collection="params.productCodes" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <!-- 根据主机厂编码和物料编码查询最新的期初库存 -->
    <select id="selectLatestOpeningInventoryByOemAndProduct" resultMap="VOResultMap">
        SELECT t.oem_code,
               t.product_code,
               IF(t.opening_inventory IS NULL, 0, t.opening_inventory) as opening_inventory,
               t.planned_date,
               t.modify_time
        FROM fdp_inventory_shift t
        JOIN (
            SELECT oem_code, product_code, MAX(modify_time) AS max_modify_time
            FROM fdp_inventory_shift
            WHERE DATE(planned_date) = CURDATE()
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                AND oem_code IN
                <foreach collection="params.oemCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                AND product_code IN
                <foreach collection="params.productCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            GROUP BY oem_code, product_code
        ) latest
        ON t.oem_code = latest.oem_code
        AND t.product_code = latest.product_code
        AND t.modify_time = latest.max_modify_time
        WHERE DATE(t.planned_date) = CURDATE()
    </select>

    <!-- 根据最新版本号查询receive汇总数据 -->
    <select id="selectLatestReceiveSummaryByVersion" resultMap="VOResultMap">
        SELECT t.oem_code,
               t.product_code,
               SUM(IF(t.receive IS NULL, 0, t.receive)) as receive
        FROM fdp_inventory_shift t
        WHERE t.version_id = (
            SELECT id
            FROM fdp_delivery_plan_version
            ORDER BY modify_time DESC
            LIMIT 1
        )
        <if test="params.oemCodes != null and params.oemCodes.size() > 0">
            AND t.oem_code IN
            <foreach collection="params.oemCodes" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.productCodes != null and params.productCodes.size() > 0">
            AND t.product_code IN
            <foreach collection="params.productCodes" item="item" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY t.oem_code, t.product_code
    </select>
</mapper>
