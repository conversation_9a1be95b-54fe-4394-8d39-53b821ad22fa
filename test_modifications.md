# 修改总结

## 修改内容

根据您的需求，我已经成功修改了 `bohStock` 和 `transportingQty` 的取值逻辑：

### 1. bohStock（中转库存）修改

**原逻辑**：
- 从多个表汇总计算（mds_oem_stock_point_map、fdp_inventory_batch_detail、fdp_oem_inventory_submission）

**新逻辑**：
- 根据主机厂编码和物料编码取 `fdp_inventory_shift` 表 `modify_time` 最新且 `planned_date=当前日期`（只需要到天的维度）的第一条数据的 `opening_inventory`

### 2. transportingQty（在途库存）修改

**原逻辑**：
- 从仓库发货记录计算（fdp_warehouse_release_record、fdp_warehouse_release_to_warehouse）

**新逻辑**：
- 对应 `fdp_inventory_shift` 表最新修改的同一个版本号的 `receive` 汇总

## 修改的文件

### 1. InventoryShiftDao.java
- 添加了两个新的查询方法：
  - `selectLatestOpeningInventoryByOemAndProduct`
  - `selectLatestReceiveSummaryByVersion`

### 2. InventoryShiftDao.xml
- 添加了对应的 SQL 查询：
  - `selectLatestOpeningInventoryByOemAndProduct`：查询最新的期初库存
  - `selectLatestReceiveSummaryByVersion`：查询最新版本的 receive 汇总

### 3. InventoryShiftService.java
- 在接口中添加了两个新方法的声明

### 4. InventoryShiftServiceImpl.java
- 实现了两个新方法

### 5. DemandDeliveryProductionServiceImpl.java
- 修改了 `getTransitStockMap` 方法（bohStock 逻辑）
- 修改了 `getTransportingQtyMap` 方法（transportingQty 逻辑）

## 核心 SQL 查询

### bohStock 查询
```sql
SELECT t.oem_code,
       t.product_code,
       IF(t.opening_inventory IS NULL, 0, t.opening_inventory) as opening_inventory,
       t.planned_date,
       t.modify_time
FROM fdp_inventory_shift t
JOIN (
    SELECT oem_code, product_code, MAX(modify_time) AS max_modify_time
    FROM fdp_inventory_shift
    WHERE DATE(planned_date) = CURDATE()
    GROUP BY oem_code, product_code
) latest
ON t.oem_code = latest.oem_code
AND t.product_code = latest.product_code
AND t.modify_time = latest.max_modify_time
WHERE DATE(t.planned_date) = CURDATE()
```

### transportingQty 查询
```sql
SELECT t.oem_code,
       t.product_code,
       SUM(IF(t.receive IS NULL, 0, t.receive)) as receive
FROM fdp_inventory_shift t
WHERE t.version_id = (
    SELECT id 
    FROM fdp_delivery_plan_version 
    ORDER BY modify_time DESC 
    LIMIT 1
)
GROUP BY t.oem_code, t.product_code
```

## 修改验证

修改已完成，新的逻辑将：
1. 对于 bohStock：查询 fdp_inventory_shift 表中当前日期最新修改时间的 opening_inventory
2. 对于 transportingQty：查询 fdp_inventory_shift 表中最新版本的 receive 字段汇总

这些修改符合您的业务需求，将数据源统一到 fdp_inventory_shift 表，提高了数据的一致性和准确性。
